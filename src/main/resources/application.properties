spring.application.name=tj-matomo-api


server.port=8081


spring.datasource.url=*************************************************************************************************************************************
spring.datasource.username=SYS20250010
spring.datasource.password=x7Y9qL2pV4bN8cD0
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis Plus
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# Redis
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.timeout=2000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0
spring.data.redis.lettuce.pool.max-wait=-1ms

logging.level.cn.edu.tongji.matomo=INFO
logging.level.org.springframework.web=INFO
