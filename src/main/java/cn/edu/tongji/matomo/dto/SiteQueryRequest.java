package cn.edu.tongji.matomo.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 站点查询请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SiteQueryRequest implements Serializable {

    @NotBlank(message = "URL不能为空")
    @Pattern(regexp = "^(https?://)?(([\\w\\-]+\\.)+[a-zA-Z]{2,})(:[0-9]+)?(/.*)?$",
             message = "URL格式不正确")
    private String url;
}
