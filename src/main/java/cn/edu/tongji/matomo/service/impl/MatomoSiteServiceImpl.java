package cn.edu.tongji.matomo.service.impl;

import cn.edu.tongji.matomo.constant.CacheConstants;
import cn.edu.tongji.matomo.dto.SiteQueryResponse;
import cn.edu.tongji.matomo.entity.MatomoSite;
import cn.edu.tongji.matomo.mapper.MatomoSiteMapper;
import cn.edu.tongji.matomo.service.MatomoSiteService;
import cn.edu.tongji.matomo.util.UrlUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Matomo站点服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatomoSiteServiceImpl implements MatomoSiteService {

    private final MatomoSiteMapper matomoSiteMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public SiteQueryResponse findSiteByUrl(String url) {
        log.info("查询URL对应的站点信息: {}", url);

        // 标准化URL（去除协议前缀和尾部斜杠）
        String normalizedUrl = UrlUtils.normalizeUrl(url);
        String cacheKey = CacheConstants.REDIS_KEY_PREFIX + normalizedUrl;

        // 1. 先从Redis缓存查询
        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                log.info("从Redis缓存中找到数据: {}", cachedData);
                if (cachedData instanceof SiteQueryResponse response) {
                    response.setFromCache(true);
                    return response;
                }
            }
        } catch (Exception e) {
            log.warn("Redis查询异常，将从数据库查询: {}", e.getMessage());
        }

        // 2. 从数据库查询
        MatomoSite site = matomoSiteMapper.selectOne(
                new QueryWrapper<MatomoSite>().like("main_url", normalizedUrl)
        );

        if (site == null) {
            log.warn("未找到匹配的站点信息: {}", normalizedUrl);
            return null;
        }
        // 3. 构建响应对象
        SiteQueryResponse response = SiteQueryResponse.builder()
            .idsite(site.getIdsite())
            .mainUrl(site.getMainUrl())
            .name(site.getName())
            .fromCache(false)
            .build();

        // 4. 存入Redis缓存
        try {
            redisTemplate.opsForValue().set(cacheKey, response, CacheConstants.CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.info("已将数据存入Redis缓存: {}", cacheKey);
        } catch (Exception e) {
            log.warn("Redis存储异常: {}", e.getMessage());
        }

        log.info("从数据库查询到站点信息: {}", response);
        return response;
    }
    

}
