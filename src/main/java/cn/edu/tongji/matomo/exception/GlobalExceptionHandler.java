package cn.edu.tongji.matomo.exception;

import cn.edu.tongji.matomo.common.ResultCommon;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultCommon<Void> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数校验失败", e);

        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + " " + error.getDefaultMessage())
                .collect(Collectors.joining("; ", "参数校验失败: ", ""));

        return ResultCommon.badRequest(message);
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResultCommon<Void> handleBindException(BindException e) {
        log.warn("参数绑定失败", e);
        return ResultCommon.badRequest("参数绑定失败: " + e.getMessage());
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResultCommon<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return ResultCommon.error("系统异常: " + e.getMessage());
    }
}
