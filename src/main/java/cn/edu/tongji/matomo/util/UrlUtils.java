package cn.edu.tongji.matomo.util;

import cn.hutool.core.util.StrUtil;

/**
 * URL工具类
 */
public class UrlUtils {
    
    /**
     * 标准化URL
     * 去除协议前缀（http://、https://）和尾部斜杠
     * 
     * @param url 原始URL
     * @return 标准化后的URL
     */
    public static String normalizeUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        
        String normalized = url.trim().toLowerCase();
        
        // 使用Hutool去除协议前缀
        normalized = StrUtil.removePrefix(normalized, "https://");
        normalized = StrUtil.removePrefix(normalized, "http://");
        
        // 去除尾部斜杠
        normalized = StrUtil.removeSuffix(normalized, "/");
        
        return normalized;
    }
    
    /**
     * 验证URL格式
     * 
     * @param url 待验证的URL
     * @return 是否为有效URL
     */
    public static boolean isValidUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        
        // 简单的URL格式验证
        String urlPattern = "^(https?://)?(([\\w\\-]+\\.)+[a-zA-Z]{2,})(:[0-9]+)?(/.*)?$";
        return url.matches(urlPattern);
    }
    
    /**
     * 私有构造函数，防止实例化
     */
    private UrlUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
