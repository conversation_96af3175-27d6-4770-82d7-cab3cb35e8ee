package cn.edu.tongji.matomo.constant;

/**
 * 缓存相关常量
 */
public class CacheConstants {
    
    /**
     * Redis缓存key前缀
     */
    public static final String REDIS_KEY_PREFIX = "matomo:site:url:";
    
    /**
     * 缓存过期时间（小时）
     */
    public static final long CACHE_EXPIRE_HOURS = 24;
    
    /**
     * 私有构造函数，防止实例化
     */
    private CacheConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
