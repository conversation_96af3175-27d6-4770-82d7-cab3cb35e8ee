package cn.edu.tongji.matomo.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 统一响应结果封装类
 * 
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultCommon<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 请求是否成功
     */
    private Boolean success;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 构造成功响应
     */
    public static <T> ResultCommon<T> success(T data) {
        return new ResultCommon<>(200, "操作成功", data, true, System.currentTimeMillis());
    }
    
    /**
     * 构造成功响应（带自定义消息）
     */
    public static <T> ResultCommon<T> success(String message, T data) {
        return new ResultCommon<>(200, message, data, true, System.currentTimeMillis());
    }
    
    /**
     * 构造成功响应（无数据）
     */
    public static <T> ResultCommon<T> success() {
        return new ResultCommon<>(200, "操作成功", null, true, System.currentTimeMillis());
    }
    
    /**
     * 构造失败响应
     */
    public static <T> ResultCommon<T> error(String message) {
        return new ResultCommon<>(500, message, null, false, System.currentTimeMillis());
    }
    
    /**
     * 构造失败响应（带状态码）
     */
    public static <T> ResultCommon<T> error(Integer code, String message) {
        return new ResultCommon<>(code, message, null, false, System.currentTimeMillis());
    }
    
    /**
     * 构造失败响应（带数据）
     */
    public static <T> ResultCommon<T> error(String message, T data) {
        return new ResultCommon<>(500, message, data, false, System.currentTimeMillis());
    }
    
    /**
     * 构造参数错误响应
     */
    public static <T> ResultCommon<T> badRequest(String message) {
        return new ResultCommon<>(400, message, null, false, System.currentTimeMillis());
    }
    
    /**
     * 构造未找到响应
     */
    public static <T> ResultCommon<T> notFound(String message) {
        return new ResultCommon<>(404, message, null, false, System.currentTimeMillis());
    }
}
