package cn.edu.tongji.matomo.controller;

import cn.edu.tongji.matomo.common.ResultCommon;
import cn.edu.tongji.matomo.dto.SiteQueryRequest;
import cn.edu.tongji.matomo.dto.SiteQueryResponse;
import cn.edu.tongji.matomo.service.MatomoSiteService;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Matomo站点控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/matomo")
@RequiredArgsConstructor
public class MatomoSiteController {
    private final MatomoSiteService matomoSiteService;
    
    /**
     * 根据URL查询站点信息
     * <p>
     * 该接口首先从Redis缓存中查询URL对应的站点信息，
     * 如果缓存中没有，则从数据库中模糊查询，并将结果存入缓存。
     *
     * @param request 包含URL的请求对象，URL格式需要符合正则表达式验证
     * @return 响应结果
     */
    @PostMapping("/site/query")
    public ResultCommon<SiteQueryResponse> querySiteByUrl(@Valid @RequestBody SiteQueryRequest request) {
        try {
            SiteQueryResponse siteInfo = matomoSiteService.findSiteByUrl(request.getUrl());

            if (siteInfo == null) {
                return ResultCommon.notFound("未找到匹配的站点信息");
            }
            return ResultCommon.success("查询成功", siteInfo);
        } catch (Exception e) {
            log.error("站点查询异常", e);
            return ResultCommon.error("查询异常: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResultCommon<Map<String, String>> health() {
        Map<String, String> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("service", "tj-matomo-api");
        return ResultCommon.success("服务正常", healthInfo);
    }
}
