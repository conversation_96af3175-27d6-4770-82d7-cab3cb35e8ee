package cn.edu.tongji.matomo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * Matomo站点实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("matomo_site")
public class MatomoSite {

    @TableId(type = IdType.AUTO)
    private Long idsite;

    private String name;

    private String mainUrl;

    private LocalDateTime tsCreated;

    private Integer ecommerce;

    private Integer sitesearch;

    private String sitesearchKeywordParameters;

    private String sitesearchCategoryParameters;

    private String timezone;

    private String currency;

    private Integer excludeUnknownUrls;

    private String excludedIps;

    private String excludedParameters;

    private String excludedUserAgents;

    private Integer keepUrlFragment;

    private String creatorLogin;
}
